<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8c5554ca-5b1b-4f49-92a2-73ad4e73be95" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$USER_HOME$/Downloads/Compressed/apache-maven-3.6.3" />
        <option name="localRepository" value="$USER_HOME$/.m2/repository_mdd" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="32JBGDG3FpgnftKK0UmdA1owENz" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showVisibilityIcons" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "HTTP Request.generated-requests | #23.executor": "Run",
    "HTTP Request.generated-requests | #24.executor": "Run",
    "HTTP Request.generated-requests | #26.executor": "Run",
    "JUnit.ComplexEquityCalculatorTests.executor": "Run",
    "JUnit.EquityCalculatorTests.testIndirectHoldingCalculation.executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.EquityCalculatorApplication.executor": "Debug",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/test/java/com/example/equity" />
    </key>
  </component>
  <component name="RunManager" selected="JUnit.ComplexEquityCalculatorTests">
    <configuration name="generated-requests | #23" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="23" requestIdentifier="#23" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #24" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="24" requestIdentifier="#24" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="generated-requests | #26" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" index="26" requestIdentifier="#26" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="ComplexEquityCalculatorTests" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="equity-calculator" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.equity.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.example.equity" />
      <option name="MAIN_CLASS_NAME" value="com.example.equity.ComplexEquityCalculatorTests" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EquityCalculatorTests.testIndirectHoldingCalculation" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="equity-calculator" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.example.equity.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.example.equity" />
      <option name="MAIN_CLASS_NAME" value="com.example.equity.EquityCalculatorTests" />
      <option name="METHOD_NAME" value="testIndirectHoldingCalculation" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EquityCalculatorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="equity-calculator" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.equity.EquityCalculatorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.ComplexEquityCalculatorTests" />
        <item itemvalue="JUnit.EquityCalculatorTests.testIndirectHoldingCalculation" />
        <item itemvalue="HTTP Request.generated-requests | #26" />
        <item itemvalue="HTTP Request.generated-requests | #24" />
        <item itemvalue="HTTP Request.generated-requests | #23" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8c5554ca-5b1b-4f49-92a2-73ad4e73be95" name="Changes" comment="" />
      <created>1757128697158</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1757128697158</updated>
      <workItem from="1757128698100" duration="1018000" />
      <workItem from="1757130165837" duration="696000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>