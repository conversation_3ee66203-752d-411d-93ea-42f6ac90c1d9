package com.example.equity.repository;

import com.example.equity.model.EquityRelationship;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EquityRelationshipRepository extends JpaRepository<EquityRelationship, Long> {
    
    @Query("SELECT er FROM EquityRelationship er WHERE er.isCurrent = true")
    List<EquityRelationship> findCurrent();
    
    @Query("SELECT er FROM EquityRelationship er WHERE er.parent.id = :parentId AND er.child.id = :childId AND er.isCurrent = true")
    EquityRelationship findByParentAndChild(@Param("parentId") Long parentId, @Param("childId") Long childId);
}
