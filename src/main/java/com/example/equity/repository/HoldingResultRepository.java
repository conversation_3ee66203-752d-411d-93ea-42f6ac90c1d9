package com.example.equity.repository;

import com.example.equity.model.HoldingResult;
import com.example.equity.model.HoldingResultId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface HoldingResultRepository extends JpaRepository<HoldingResult, HoldingResultId> {
    
    @Query("SELECT hr FROM HoldingResult hr WHERE hr.parent.id = :parentId AND hr.child.id = :childId")
    Optional<HoldingResult> findByParentAndChild(@Param("parentId") Long parentId, @Param("childId") Long childId);
    
    @Query("SELECT hr FROM HoldingResult hr WHERE hr.indirectRatio > :threshold")
    List<HoldingResult> findByRatioAbove(@Param("threshold") BigDecimal threshold);

    @Query("SELECT hr FROM HoldingResult hr WHERE hr.parent.id = :parentId AND hr.child.id IN :childIds")
    List<HoldingResult> findByParentIdAndChildIds(
        @Param("parentId") Long parentId,
        @Param("childIds") Collection<Long> childIds);
}
