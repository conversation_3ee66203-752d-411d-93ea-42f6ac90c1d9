package com.example.equity.controller;

import com.example.equity.test.LargeScaleTestDataGenerator;
import com.example.equity.test.PerformanceTestRunner;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {

    private final LargeScaleTestDataGenerator testDataGenerator;
    private final PerformanceTestRunner performanceTestRunner;

    public TestController(
            LargeScaleTestDataGenerator testDataGenerator,
            PerformanceTestRunner performanceTestRunner) {
        this.testDataGenerator = testDataGenerator;
        this.performanceTestRunner = performanceTestRunner;
    }

    /**
     * 生成10,000家公司测试数据
     */
    @PostMapping("/generate-data")
    public ResponseEntity<?> generateTestData() {
        try {
            long start = System.currentTimeMillis();
            testDataGenerator.generateFullTestData();
            long time = System.currentTimeMillis() - start;
            
            return ResponseEntity.ok(Map.of(
                "message", "测试数据生成完成",
                "duration", time + "ms"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "error", "数据生成失败",
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 运行性能测试
     */
    @PostMapping("/performance")
    public ResponseEntity<?> runPerformanceTests() {
        try {
            long start = System.currentTimeMillis();
            
            // 运行性能测试（模拟CommandLineRunner的行为）
            performanceTestRunner.run("performance-test");
            
            long time = System.currentTimeMillis() - start;
            
            return ResponseEntity.ok(Map.of(
                "message", "性能测试完成",
                "duration", time + "ms"
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "error", "性能测试失败",
                "message", e.getMessage()
            ));
        }
    }

    /**
     * 获取测试状态
     */
    @GetMapping("/status")
    public Map<String, Object> getTestStatus() {
        // 这里可以添加更多的状态信息
        return Map.of(
            "message", "测试系统就绪",
            "timestamp", System.currentTimeMillis()
        );
    }
}
