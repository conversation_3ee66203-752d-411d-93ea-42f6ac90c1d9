package com.example.equity.controller;

import com.example.equity.calculation.SmartEquityCalculator;
import com.example.equity.model.*;
import com.example.equity.repository.*;
import com.example.equity.service.EquityService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/equity")
public class EquityController {

    private final EquityService equityService;
    private final CompanyRepository companyRepository;
    private final EquityRelationshipRepository equityRelationshipRepository;
    private final HoldingResultRepository holdingResultRepository;
    private final SmartEquityCalculator smartEquityCalculator;

    public EquityController(
            EquityService equityService,
            CompanyRepository companyRepository,
            EquityRelationshipRepository equityRelationshipRepository,
            HoldingResultRepository holdingResultRepository,
            SmartEquityCalculator smartEquityCalculator) {
        this.equityService = equityService;
        this.companyRepository = companyRepository;
        this.equityRelationshipRepository = equityRelationshipRepository;
        this.holdingResultRepository = holdingResultRepository;
        this.smartEquityCalculator = smartEquityCalculator;
    }

    // ====================== 公司管理 ======================
    
    @GetMapping("/companies")
    public List<Company> getAllCompanies() {
        return companyRepository.findAll();
    }
    
    @PostMapping("/companies")
    public Company createCompany(@RequestBody Company company) {
        return companyRepository.save(company);
    }
    
    // ====================== 股权关系管理 ======================
    
    @GetMapping("/relationships")
    public List<EquityRelationship> getAllRelationships() {
        return equityRelationshipRepository.findAll();
    }
    
    @GetMapping("/relationships/current")
    public List<EquityRelationship> getCurrentRelationships() {
        return equityRelationshipRepository.findCurrent();
    }
    
    @PostMapping("/relationships")
    public ResponseEntity<?> updateRelationships(@RequestBody List<EquityService.EquityChangeRequest> changes) {
        try {
            int affectedCount = equityService.updateEquityRelationships(changes);
            return ResponseEntity.accepted().body(Map.of(
                "message", "变更已提交",
                "affectedCount", affectedCount,
                "statusUrl", "/api/equity/status/" + UUID.randomUUID()
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "error", "更新失败",
                "message", e.getMessage()
            ));
        }
    }
    
    // ====================== 持股查询 ======================
    
    @GetMapping("/holding/{parent}/{child}")
    public BigDecimal getHolding(@PathVariable Long parent, @PathVariable Long child) {
        return equityService.getIndirectHolding(parent, child);
    }
    
    @GetMapping("/holding/controlled")
    public List<Map<String, Object>> getControlledCompanies(
            @RequestParam Long companyId,
            @RequestParam(required = false, defaultValue = "0.5") BigDecimal threshold) {
        
        List<Company> controlled = equityService.getControlledCompanies(companyId, threshold);
        return controlled.stream()
                .map(company -> {
                    BigDecimal ratio = equityService.getIndirectHolding(companyId, company.getId());
                    return Map.of(
                        "company", company,
                        "holdingRatio", ratio
                    );
                })
                .collect(Collectors.toList());
    }
    
    @GetMapping("/holding/matrix")
    public List<Map<String, Object>> getHoldingMatrix() {
        List<HoldingResult> results = holdingResultRepository.findAll();
        return results.stream()
                .filter(hr -> hr.getIndirectRatio().compareTo(BigDecimal.ZERO) > 0)
                .map(hr -> Map.of(
                    "parent", hr.getParent(),
                    "child", hr.getChild(),
                    "ratio", hr.getIndirectRatio(),
                    "calculationTime", hr.getCalculationTime()
                ))
                .collect(Collectors.toList());
    }
    
    // ====================== 计算管理 ======================
    
    @PostMapping("/calculate/full")
    public ResponseEntity<?> performFullCalculation() {
        try {
            int resultCount = equityService.performFullCalculation();
            return ResponseEntity.ok(Map.of(
                "message", "全量计算完成",
                "resultCount", resultCount
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "error", "计算失败",
                "message", e.getMessage()
            ));
        }
    }
    
    // ====================== 系统状态 ======================
    
    @GetMapping("/status")
    public Map<String, Object> getSystemStatus() {
        long companyCount = companyRepository.count();
        long relationshipCount = equityRelationshipRepository.count();
        long resultCount = holdingResultRepository.count();
        
        return Map.of(
            "companyCount", companyCount,
            "relationshipCount", relationshipCount,
            "resultCount", resultCount,
            "timestamp", System.currentTimeMillis()
        );
    }
}
