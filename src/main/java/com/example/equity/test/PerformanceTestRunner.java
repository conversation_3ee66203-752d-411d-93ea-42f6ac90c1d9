package com.example.equity.test;

import com.example.equity.calculation.EquityCalculator;
import com.example.equity.calculation.SmartEquityCalculator;
import com.example.equity.model.Company;
import com.example.equity.model.EquityRelationship;
import com.example.equity.repository.CompanyRepository;
import com.example.equity.repository.EquityRelationshipRepository;
import com.example.equity.repository.HoldingResultRepository;
import com.example.equity.service.impl.JdbcDatabaseSaver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
public class PerformanceTestRunner implements CommandLineRunner {

    private final LargeScaleTestDataGenerator testDataGenerator;
    private final CompanyRepository companyRepository;
    private final EquityRelationshipRepository equityRelationshipRepository;
    private final HoldingResultRepository holdingResultRepository;
    
    @Autowired
    public PerformanceTestRunner(
            LargeScaleTestDataGenerator testDataGenerator,
            CompanyRepository companyRepository,
            EquityRelationshipRepository equityRelationshipRepository,
            HoldingResultRepository holdingResultRepository) {
        this.testDataGenerator = testDataGenerator;
        this.companyRepository = companyRepository;
        this.equityRelationshipRepository = equityRelationshipRepository;
        this.holdingResultRepository = holdingResultRepository;
    }

    @Override
    public void run(String... args) {
        // Check if we should run performance tests
        if (args.length > 0 && "performance-test".equals(args[0])) {
            // 1. 生成10,000家公司测试数据
            testDataGenerator.generateFullTestData();
            
            // 2. 运行性能测试
            runPerformanceTests();
        } else {
            System.out.println("Skipping performance tests. Use 'performance-test' argument to run.");
        }
    }
    
    private void runPerformanceTests() {
        System.out.println("\n===== 开始全面性能测试 =====");
        
        // 1. 基准测试
        runBaselineTests();
        
        // 2. 增量计算测试
        runIncrementalTests();
        
        // 3. 循环持股测试
        runCircularHoldingTests();
        
        System.out.println("\n===== 性能测试完成 =====");
    }
    
    private void runBaselineTests() {
        System.out.println("\n--- 基准测试 ---");
        
        // 1. 准备计算引擎
        List<EquityCalculator.EquityRelationship> relationships = equityRelationshipRepository.findCurrent().stream()
            .map(er -> new EquityCalculator.EquityRelationship(
                er.getParentId(), 
                er.getChildId(), 
                er.getRatio()
            ))
            .collect(Collectors.toList());
        
        EquityCalculator calculator = new EquityCalculator(relationships, 15);
        
        // 2. 随机选择测试点
        List<Company> companies = companyRepository.findAll();
        Random random = ThreadLocalRandom.current();
        
        // 3. 测试单次计算
        testSingleCalculation(calculator, companies, random);
        
        // 4. 测试100次随机计算
        testMultipleCalculations(calculator, companies, random, 100);
        
        // 5. 测试样本计算
        testSampleCalculation(calculator, companies);
    }
    
    private void testSingleCalculation(
            EquityCalculator calculator, 
            List<Company> companies, 
            Random random) {
        
        Company from = companies.get(random.nextInt(companies.size()));
        Company to = companies.get(random.nextInt(companies.size()));
        while (from.equals(to)) {
            to = companies.get(random.nextInt(companies.size()));
        }
        
        System.out.println("测试单次计算: " + from.getName() + " → " + to.getName());
        
        long start = System.currentTimeMillis();
        BigDecimal result = calculator.calculateIndirectHolding(from.getId(), to.getId());
        long time = System.currentTimeMillis() - start;
        
        System.out.println("✓ 结果: " + result + " (耗时: " + time + "ms)");
    }
    
    private void testMultipleCalculations(
            EquityCalculator calculator, 
            List<Company> companies, 
            Random random,
            int count) {
        
        System.out.println("\n测试" + count + "次随机计算:");
        
        long start = System.currentTimeMillis();
        List<Long> times = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            Company from = companies.get(random.nextInt(companies.size()));
            Company to = companies.get(random.nextInt(companies.size()));
            while (from.equals(to)) {
                to = companies.get(random.nextInt(companies.size()));
            }
            
            long calcStart = System.currentTimeMillis();
            calculator.calculateIndirectHolding(from.getId(), to.getId());
            long calcTime = System.currentTimeMillis() - calcStart;
            times.add(calcTime);
        }
        
        long total = System.currentTimeMillis() - start;
        double avg = times.stream().mapToLong(l -> l).average().orElse(0);
        long min = Collections.min(times);
        long max = Collections.max(times);
        
        System.out.println("✓ 总耗时: " + total + "ms");
        System.out.println("  平均: " + String.format("%.2f", avg) + "ms");
        System.out.println("  最小: " + min + "ms");
        System.out.println("  最大: " + max + "ms");
    }

    private void testSampleCalculation(EquityCalculator calculator, List<Company> companies) {
        System.out.println("\n测试样本计算 (1000家公司样本):");

        long start = System.currentTimeMillis();
        int pairs = 0;

        // 只计算部分样本，避免太长时间
        int sampleSize = Math.min(1000, companies.size());
        for (int i = 0; i < sampleSize; i++) {
            for (int j = 0; j < sampleSize; j++) {
                if (i != j) {
                    calculator.calculateIndirectHolding(
                        companies.get(i).getId(),
                        companies.get(j).getId());
                    pairs++;
                }
            }
        }

        long time = System.currentTimeMillis() - start;
        double avg = (double) time / pairs;

        System.out.println("✓ 样本计算完成: " + pairs + " 对关系 (" + time + "ms)");
        System.out.println("  平均: " + String.format("%.4f", avg) + "ms/对");

        // 推算10,000家公司全量计算时间
        long estimatedTotal = (long) (avg * 10000L * 9999);
        System.out.println("  预估全量计算: " + formatTime(estimatedTotal));
    }

    private void runIncrementalTests() {
        System.out.println("\n--- 增量计算测试 ---");

        // 1. 准备数据
        List<Company> companies = companyRepository.findAll();
        List<EquityRelationship> currentRelationships = equityRelationshipRepository.findCurrent();
        Random random = ThreadLocalRandom.current();

        // 2. 创建增量计算引擎
        List<EquityCalculator.EquityRelationship> calcRelationships = currentRelationships.stream()
            .map(er -> new EquityCalculator.EquityRelationship(
                er.getParentId(),
                er.getChildId(),
                er.getRatio()
            ))
            .collect(Collectors.toList());

        JdbcDatabaseSaver databaseSaver = new JdbcDatabaseSaver(companyRepository, holdingResultRepository);
        SmartEquityCalculator smartCalculator = new SmartEquityCalculator(calcRelationships, 15, databaseSaver);

        // 3. 测试小变更（修改1条关系）
        testSmallChange(smartCalculator, currentRelationships, random);
    }

    private void testSmallChange(
            SmartEquityCalculator smartCalculator,
            List<EquityRelationship> currentRelationships,
            Random random) {

        System.out.println("\n测试小变更 (1条关系):");

        if (currentRelationships.isEmpty()) {
            System.out.println("✗ 没有现有关系可以修改");
            return;
        }

        // 选择一条随机关系进行修改
        EquityRelationship original = currentRelationships.get(random.nextInt(currentRelationships.size()));
        BigDecimal newRatio = original.getRatio().add(new BigDecimal("0.1")).min(BigDecimal.ONE);

        // 准备变更
        List<EquityCalculator.EquityRelationship> added = Collections.emptyList();
        List<EquityCalculator.EquityRelationship> updatedList = Collections.singletonList(
            new EquityCalculator.EquityRelationship(
                original.getParentId(),
                original.getChildId(),
                newRatio
            )
        );
        List<EquityCalculator.EquityRelationship> deleted = Collections.singletonList(
            new EquityCalculator.EquityRelationship(
                original.getParentId(),
                original.getChildId(),
                original.getRatio()
            )
        );

        // 执行变更
        long start = System.currentTimeMillis();
        int affected = smartCalculator.handleChanges(added, updatedList, deleted);
        long time = System.currentTimeMillis() - start;

        System.out.println("✓ 变更完成: 更新了 " + affected + " 个结果 (" + time + "ms)");
    }

    private void runCircularHoldingTests() {
        System.out.println("\n--- 循环持股测试 ---");

        // 1. 准备数据
        List<Company> companies = companyRepository.findAll();
        List<EquityRelationship> currentRelationships = equityRelationshipRepository.findCurrent();

        // 2. 创建计算引擎
        List<EquityCalculator.EquityRelationship> relationships = currentRelationships.stream()
            .map(er -> new EquityCalculator.EquityRelationship(
                er.getParentId(),
                er.getChildId(),
                er.getRatio()
            ))
            .collect(Collectors.toList());

        EquityCalculator calculator = new EquityCalculator(relationships, 15);

        // 3. 测试已知循环案例
        testKnownCircularCase(calculator, "REGION_1", "PROV_1", "GROUP");
        testKnownCircularCase(calculator, "FIN_HLDG", "SECUR_1", "INSUR_1");
        testKnownCircularCase(calculator, "PROV_5", "CITY_50", "GRASS_500");
    }

    private void testKnownCircularCase(
            EquityCalculator calculator,
            String companyA,
            String companyB,
            String companyC) {

        System.out.println("\n测试循环案例: " + companyA + " → " + companyB + " → " + companyC + " → " + companyA);

        Company a = findCompanyByCode(companyA);
        Company b = findCompanyByCode(companyB);
        Company c = findCompanyByCode(companyC);

        if (a == null || b == null || c == null) {
            System.out.println("✗ 公司不存在");
            return;
        }

        // 测试A→C的间接持股（应包含循环影响）
        long start = System.currentTimeMillis();
        BigDecimal result = calculator.calculateIndirectHolding(a.getId(), c.getId());
        long time = System.currentTimeMillis() - start;

        System.out.println("✓ A→C计算: " + result + " (耗时: " + time + "ms)");

        // 验证是否检测到循环
        boolean hasCycleA = calculator.hasCircularOwnership(a.getId());
        boolean hasCycleB = calculator.hasCircularOwnership(b.getId());
        boolean hasCycleC = calculator.hasCircularOwnership(c.getId());

        System.out.println("  循环检测: A=" + hasCycleA + ", B=" + hasCycleB + ", C=" + hasCycleC);
    }

    private Company findCompanyByCode(String code) {
        return companyRepository.findAll().stream()
            .filter(c -> c.getCode().equals(code))
            .findFirst()
            .orElse(null);
    }

    private String formatTime(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + "ms";
        } else if (milliseconds < 60000) {
            return String.format("%.1f秒", milliseconds / 1000.0);
        } else if (milliseconds < 3600000) {
            return String.format("%.1f分钟", milliseconds / 60000.0);
        } else {
            return String.format("%.1f小时", milliseconds / 3600000.0);
        }
    }
}
