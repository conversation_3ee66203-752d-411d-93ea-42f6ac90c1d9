package com.example.equity.test;

import com.example.equity.model.Company;
import com.example.equity.model.EquityRelationship;
import com.example.equity.repository.CompanyRepository;
import com.example.equity.repository.EquityRelationshipRepository;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@Component
public class LargeScaleTestDataGenerator {

    // 架构参数
    private static final int TOTAL_COMPANIES = 10_000;
    private static final int REGIONAL_HEADQUARTERS = 5;
    private static final int PROVINCIAL_COMPANIES = 50;
    private static final int CITY_COMPANIES = 500;
    private static final int GRASSROOTS_COMPANIES = 4_445;
    private static final int SPECIALIZED_SUBSIDIARIES = 500;
    private static final int SECOND_LEVEL_SPECIALIZED = 2_500;
    private static final int THIRD_LEVEL_SPECIALIZED = 1_450;
    private static final int FINANCIAL_HOLDING = 1;
    private static final int INSURANCE_COMPANIES = 10;
    private static final int BANKS = 5;
    private static final int SECURITIES_COMPANIES = 5;
    private static final int OTHER_FINANCIAL = 30;
    
    // 持股比例配置
    private static final double[] CONTROL_RATIOS = {0.51, 0.55, 0.60, 0.67, 0.75, 0.85, 0.90, 1.0};
    private static final double[] MAJOR_INFLUENCE_RATIOS = {0.20, 0.25, 0.30, 0.35, 0.40, 0.45, 0.49};
    private static final double[] MINOR_HOLDING_RATIOS = {0.001, 0.005, 0.01, 0.05, 0.10, 0.15};
    
    private final CompanyRepository companyRepository;
    private final EquityRelationshipRepository equityRelationshipRepository;
    
    public LargeScaleTestDataGenerator(
            CompanyRepository companyRepository,
            EquityRelationshipRepository equityRelationshipRepository) {
        this.companyRepository = companyRepository;
        this.equityRelationshipRepository = equityRelationshipRepository;
    }
    
    /**
     * 生成10,000家公司及其股权关系
     */
    public void generateFullTestData() {
        System.out.println("开始生成10,000家公司测试数据...");
        long start = System.currentTimeMillis();
        
        // 1. 清空现有数据
        clearExistingData();
        
        // 2. 生成所有公司
        List<Company> companies = generateAllCompanies();
        System.out.println("✓ 生成 " + companies.size() + " 家公司");
        
        // 3. 构建标准股权结构
        List<EquityRelationship> standardStructure = buildStandardStructure(companies);
        System.out.println("✓ 构建标准股权结构 (" + standardStructure.size() + " 条关系)");
        
        // 4. 添加复杂股权关系
        List<EquityRelationship> complexStructure = buildComplexStructure(companies);
        System.out.println("✓ 添加复杂股权关系 (" + complexStructure.size() + " 条关系)");
        
        // 5. 添加循环持股案例
        List<EquityRelationship> circularHoldings = buildCircularHoldings(companies);
        System.out.println("✓ 添加循环持股案例 (" + circularHoldings.size() + " 条关系)");
        
        // 6. 保存所有数据
        saveAllData(companies, standardStructure, complexStructure, circularHoldings);
        
        System.out.println("✓ 数据生成完成! 总耗时: " + (System.currentTimeMillis() - start) + "ms");
        System.out.println("总公司数: " + companies.size());
        System.out.println("总股权关系数: " + (standardStructure.size() + complexStructure.size() + circularHoldings.size()));
    }
    
    /**
     * 清空现有数据
     */
    private void clearExistingData() {
        equityRelationshipRepository.deleteAll();
        companyRepository.deleteAll();
    }
    
    /**
     * 生成所有公司
     */
    private List<Company> generateAllCompanies() {
        List<Company> companies = new ArrayList<>(TOTAL_COMPANIES);
        
        // 1. 集团总部 (1)
        companies.add(new Company("集团总部", "GROUP"));
        
        // 2. 区域总部 (5)
        for (int i = 1; i <= REGIONAL_HEADQUARTERS; i++) {
            companies.add(new Company("区域总部" + i, "REGION_" + i));
        }
        
        // 3. 省级公司 (50)
        for (int i = 1; i <= PROVINCIAL_COMPANIES; i++) {
            companies.add(new Company("省级公司" + i, "PROV_" + i));
        }
        
        // 4. 市级公司 (500)
        for (int i = 1; i <= CITY_COMPANIES; i++) {
            companies.add(new Company("市级公司" + i, "CITY_" + i));
        }
        
        // 5. 基层公司 (4,445)
        for (int i = 1; i <= GRASSROOTS_COMPANIES; i++) {
            companies.add(new Company("基层公司" + i, "GRASS_" + i));
        }
        
        // 6. 专业子公司 (500)
        for (int i = 1; i <= SPECIALIZED_SUBSIDIARIES; i++) {
            companies.add(new Company("专业子公司" + i, "SPEC_" + i));
        }
        
        // 7. 二级专业公司 (2,500)
        for (int i = 1; i <= SECOND_LEVEL_SPECIALIZED; i++) {
            companies.add(new Company("二级专业公司" + i, "SPEC2_" + i));
        }
        
        // 8. 三级专业公司 (1,450)
        for (int i = 1; i <= THIRD_LEVEL_SPECIALIZED; i++) {
            companies.add(new Company("三级专业公司" + i, "SPEC3_" + i));
        }
        
        // 9. 金融控股平台 (1)
        companies.add(new Company("金融控股平台", "FIN_HLDG"));
        
        // 10. 保险公司 (10)
        for (int i = 1; i <= INSURANCE_COMPANIES; i++) {
            companies.add(new Company("保险公司" + i, "INSUR_" + i));
        }
        
        // 11. 银行 (5)
        for (int i = 1; i <= BANKS; i++) {
            companies.add(new Company("银行" + i, "BANK_" + i));
        }
        
        // 12. 证券公司 (5)
        for (int i = 1; i <= SECURITIES_COMPANIES; i++) {
            companies.add(new Company("证券公司" + i, "SECUR_" + i));
        }
        
        // 13. 其他金融机构 (30)
        for (int i = 1; i <= OTHER_FINANCIAL; i++) {
            companies.add(new Company("金融机构" + i, "FIN_" + i));
        }
        
        // 保存到数据库
        List<Company> savedCompanies = companyRepository.saveAll(companies);
        
        // 为所有公司分配ID（H2内存数据库会自动分配ID，这里确保ID连续）
        Map<String, Long> idMap = new HashMap<>();
        for (Company company : savedCompanies) {
            idMap.put(company.getCode(), company.getId());
        }
        
        // 重新设置ID（用于后续构建关系）
        for (Company company : companies) {
            company.setId(idMap.get(company.getCode()));
        }
        
        return companies;
    }

    /**
     * 构建标准股权结构
     */
    private List<EquityRelationship> buildStandardStructure(List<Company> companies) {
        List<EquityRelationship> relationships = new ArrayList<>();
        Random random = ThreadLocalRandom.current();

        // 1. 集团总部 → 区域总部 (100%)
        Company group = findCompanyByCode(companies, "GROUP");
        for (int i = 1; i <= REGIONAL_HEADQUARTERS; i++) {
            Company regional = findCompanyByCode(companies, "REGION_" + i);
            relationships.add(new EquityRelationship(
                group, regional, getRandomControlRatio(random)));
        }

        // 2. 区域总部 → 省级公司 (85-100%)
        for (int region = 1; region <= REGIONAL_HEADQUARTERS; region++) {
            Company regional = findCompanyByCode(companies, "REGION_" + region);

            // 每个区域分配10个省级公司
            for (int prov = (region-1)*10 + 1; prov <= region*10; prov++) {
                Company provincial = findCompanyByCode(companies, "PROV_" + prov);
                relationships.add(new EquityRelationship(
                    regional, provincial, getRandomControlRatio(random)));
            }
        }

        // 3. 省级公司 → 市级公司 (75-100%)
        for (int prov = 1; prov <= PROVINCIAL_COMPANIES; prov++) {
            Company provincial = findCompanyByCode(companies, "PROV_" + prov);

            // 每个省级分配10个市级公司
            for (int city = (prov-1)*10 + 1; city <= prov*10; city++) {
                Company cityCompany = findCompanyByCode(companies, "CITY_" + city);
                relationships.add(new EquityRelationship(
                    provincial, cityCompany, getRandomControlRatio(random)));
            }
        }

        // 4. 市级公司 → 基层公司 (67-100%)
        for (int city = 1; city <= CITY_COMPANIES; city++) {
            Company cityCompany = findCompanyByCode(companies, "CITY_" + city);

            // 每个市级分配9个基层公司（500*9=4500，略多于4445）
            for (int grass = (city-1)*9 + 1; grass <= (city-1)*9 + 9; grass++) {
                if (grass > GRASSROOTS_COMPANIES) break;

                Company grassroots = findCompanyByCode(companies, "GRASS_" + grass);
                relationships.add(new EquityRelationship(
                    cityCompany, grassroots, getRandomControlRatio(random)));
            }
        }

        // 5. 专业子公司层级结构
        Company finHolding = findCompanyByCode(companies, "FIN_HLDG");

        // 5.1 集团 → 金融控股平台 (100%)
        relationships.add(new EquityRelationship(
            group, finHolding, new BigDecimal("1.0")));

        // 5.2 金融控股平台 → 保险公司 (85-100%)
        for (int i = 1; i <= INSURANCE_COMPANIES; i++) {
            Company insurance = findCompanyByCode(companies, "INSUR_" + i);
            relationships.add(new EquityRelationship(
                finHolding, insurance, getRandomControlRatio(random)));
        }

        // 5.3 金融控股平台 → 银行 (85-100%)
        for (int i = 1; i <= BANKS; i++) {
            Company bank = findCompanyByCode(companies, "BANK_" + i);
            relationships.add(new EquityRelationship(
                finHolding, bank, getRandomControlRatio(random)));
        }

        // 5.4 金融控股平台 → 证券公司 (85-100%)
        for (int i = 1; i <= SECURITIES_COMPANIES; i++) {
            Company securities = findCompanyByCode(companies, "SECUR_" + i);
            relationships.add(new EquityRelationship(
                finHolding, securities, getRandomControlRatio(random)));
        }

        // 5.5 金融控股平台 → 其他金融机构 (50-100%)
        for (int i = 1; i <= OTHER_FINANCIAL; i++) {
            Company financial = findCompanyByCode(companies, "FIN_" + i);
            relationships.add(new EquityRelationship(
                finHolding, financial, getRandomControlRatio(random)));
        }

        return relationships;
    }

    /**
     * 构建复杂股权关系（交叉持股、一致行动人等）
     */
    private List<EquityRelationship> buildComplexStructure(List<Company> companies) {
        List<EquityRelationship> relationships = new ArrayList<>();
        Random random = ThreadLocalRandom.current();

        // 1. 添加跨层级持股（300条）
        for (int i = 0; i < 300; i++) {
            Company parent = getRandomCompany(companies, random);
            Company child = getRandomCompany(companies, random);

            // 确保不是同一公司且不是已有关系
            if (!parent.equals(child) && !hasRelationship(relationships, parent, child)) {
                // 50%概率为控制性持股，30%为重大影响，20%为少量持股
                BigDecimal ratio;
                double prob = random.nextDouble();
                if (prob < 0.5) {
                    ratio = new BigDecimal(String.format("%.4f", CONTROL_RATIOS[random.nextInt(CONTROL_RATIOS.length)]));
                } else if (prob < 0.8) {
                    ratio = new BigDecimal(String.format("%.4f", MAJOR_INFLUENCE_RATIOS[random.nextInt(MAJOR_INFLUENCE_RATIOS.length)]));
                } else {
                    ratio = new BigDecimal(String.format("%.4f", MINOR_HOLDING_RATIOS[random.nextInt(MINOR_HOLDING_RATIOS.length)]));
                }

                relationships.add(new EquityRelationship(parent, child, ratio));
            }
        }

        return relationships;
    }

    /**
     * 构建循环持股案例
     */
    private List<EquityRelationship> buildCircularHoldings(List<Company> companies) {
        List<EquityRelationship> relationships = new ArrayList<>();
        Random random = ThreadLocalRandom.current();

        // 循环案例1：集团←区域←省级←集团
        Company group = findCompanyByCode(companies, "GROUP");
        Company region1 = findCompanyByCode(companies, "REGION_1");
        Company prov1 = findCompanyByCode(companies, "PROV_1");

        relationships.add(new EquityRelationship(region1, group, new BigDecimal("0.15")));
        relationships.add(new EquityRelationship(prov1, region1, new BigDecimal("0.25")));
        relationships.add(new EquityRelationship(group, prov1, new BigDecimal("0.10")));

        // 循环案例2：金融控股←保险←证券←金融控股
        Company finHolding = findCompanyByCode(companies, "FIN_HLDG");
        Company insur1 = findCompanyByCode(companies, "INSUR_1");
        Company sec1 = findCompanyByCode(companies, "SECUR_1");

        relationships.add(new EquityRelationship(insur1, finHolding, new BigDecimal("0.05")));
        relationships.add(new EquityRelationship(sec1, insur1, new BigDecimal("0.08")));
        relationships.add(new EquityRelationship(finHolding, sec1, new BigDecimal("0.03")));

        // 循环案例3：省级←市级←基层←省级
        Company prov5 = findCompanyByCode(companies, "PROV_5");
        Company city50 = findCompanyByCode(companies, "CITY_50");
        Company grass500 = findCompanyByCode(companies, "GRASS_500");

        relationships.add(new EquityRelationship(city50, prov5, new BigDecimal("0.12")));
        relationships.add(new EquityRelationship(grass500, city50, new BigDecimal("0.07")));
        relationships.add(new EquityRelationship(prov5, grass500, new BigDecimal("0.05")));

        // 添加更多随机循环（20个）
        for (int i = 0; i < 20; i++) {
            // 生成3-5个公司的循环
            int cycleLength = 3 + random.nextInt(3);
            List<Company> cycleCompanies = new ArrayList<>();

            // 选择cycleLength个不同的公司
            while (cycleCompanies.size() < cycleLength) {
                Company company = getRandomCompany(companies, random);
                if (!cycleCompanies.contains(company)) {
                    cycleCompanies.add(company);
                }
            }

            // 创建循环关系
            for (int j = 0; j < cycleLength; j++) {
                Company from = cycleCompanies.get(j);
                Company to = cycleCompanies.get((j + 1) % cycleLength);

                // 避免已存在的关系
                if (!hasRelationship(relationships, from, to)) {
                    BigDecimal ratio = new BigDecimal(String.format("%.4f",
                        0.05 + random.nextDouble() * 0.25)); // 5%-30%
                    relationships.add(new EquityRelationship(from, to, ratio));
                }
            }
        }

        return relationships;
    }

    /**
     * 保存所有数据
     */
    private void saveAllData(
            List<Company> companies,
            List<EquityRelationship> standardStructure,
            List<EquityRelationship> complexStructure,
            List<EquityRelationship> circularHoldings) {

        List<EquityRelationship> allRelationships = new ArrayList<>();
        allRelationships.addAll(standardStructure);
        allRelationships.addAll(complexStructure);
        allRelationships.addAll(circularHoldings);

        equityRelationshipRepository.saveAll(allRelationships);
    }

    // ====================== 辅助方法 ======================

    private Company findCompanyByCode(List<Company> companies, String code) {
        return companies.stream()
            .filter(c -> c.getCode().equals(code))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("Company not found: " + code));
    }

    private boolean hasRelationship(List<EquityRelationship> relationships, Company parent, Company child) {
        return relationships.stream()
            .anyMatch(r -> r.getParent().equals(parent) && r.getChild().equals(child));
    }

    private Company getRandomCompany(List<Company> companies, Random random) {
        return companies.get(random.nextInt(companies.size()));
    }

    private BigDecimal getRandomControlRatio(Random random) {
        return new BigDecimal(String.format("%.4f", CONTROL_RATIOS[random.nextInt(CONTROL_RATIOS.length)]));
    }
}
