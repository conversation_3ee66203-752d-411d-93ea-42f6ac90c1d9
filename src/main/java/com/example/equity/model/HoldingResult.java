package com.example.equity.model;

import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "holding_result")
@IdClass(HoldingResultId.class)
public class HoldingResult {
    @Id
    @ManyToOne
    @JoinColumn(name = "parent_id", nullable = false)
    private Company parent;
    
    @Id
    @ManyToOne
    @JoinColumn(name = "child_id", nullable = false)
    private Company child;
    
    @Column(nullable = false, precision = 10, scale = 6)
    private BigDecimal indirectRatio;
    
    @Column(nullable = false)
    private LocalDateTime calculationTime;
    
    // 构造函数
    public HoldingResult() {}
    
    public HoldingResult(Company parent, Company child, BigDecimal indirectRatio) {
        this.parent = parent;
        this.child = child;
        this.indirectRatio = indirectRatio;
        this.calculationTime = LocalDateTime.now();
    }
    
    // Getters and setters
    public Company getParent() { return parent; }
    public void setParent(Company parent) { this.parent = parent; }
    public Company getChild() { return child; }
    public void setChild(Company child) { this.child = child; }
    public BigDecimal getIndirectRatio() { return indirectRatio; }
    public void setIndirectRatio(BigDecimal indirectRatio) { this.indirectRatio = indirectRatio; }
    public LocalDateTime getCalculationTime() { return calculationTime; }
    public void setCalculationTime(LocalDateTime calculationTime) { this.calculationTime = calculationTime; }
    
    // 辅助方法（用于计算引擎）
    public Long getParentId() {
        return parent != null ? parent.getId() : null;
    }
    
    public Long getChildId() {
        return child != null ? child.getId() : null;
    }
}
