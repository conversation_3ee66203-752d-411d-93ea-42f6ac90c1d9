package com.example.equity.model;

import java.util.Objects;

// 复合主键类
public class HoldingResultId implements java.io.Serializable {
    private Long parent;
    private Long child;
    
    public HoldingResultId() {}
    
    public HoldingResultId(Long parent, Long child) {
        this.parent = parent;
        this.child = child;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HoldingResultId that = (HoldingResultId) o;
        return parent.equals(that.parent) && child.equals(that.child);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(parent, child);
    }
}
