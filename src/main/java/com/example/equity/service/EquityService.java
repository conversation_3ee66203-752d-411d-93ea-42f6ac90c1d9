package com.example.equity.service;

import com.example.equity.model.Company;
import com.example.equity.model.EquityRelationship;

import java.math.BigDecimal;
import java.util.List;

public interface EquityService {

    /**
     * 更新股权关系
     * @param changes 变更请求列表
     * @return 受影响的计算结果数量
     */
    int updateEquityRelationships(List<EquityChangeRequest> changes);
    
    /**
     * 获取间接持股比例
     * @param parentId 母公司ID
     * @param childId 子公司ID
     * @return 间接持股比例
     */
    BigDecimal getIndirectHolding(Long parentId, Long childId);
    
    /**
     * 获取公司控制的所有公司
     * @param companyId 公司ID
     * @param threshold 控制阈值（默认0.5）
     * @return 被控制的公司列表
     */
    List<Company> getControlledCompanies(Long companyId, BigDecimal threshold);
    
    /**
     * 执行全量计算
     * @return 更新的结果数量
     */
    int performFullCalculation();
    
    // 变更请求枚举
    enum ChangeAction {
        ADD, UPDATE, DELETE
    }
    
    // 变更请求对象
    class EquityChangeRequest {
        private final ChangeAction action;
        private final EquityRelationship relationship;
        
        public EquityChangeRequest(ChangeAction action, EquityRelationship relationship) {
            this.action = action;
            this.relationship = relationship;
        }
        
        public ChangeAction getAction() { return action; }
        public EquityRelationship getRelationship() { return relationship; }
    }
}
