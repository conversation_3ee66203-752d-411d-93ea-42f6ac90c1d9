package com.example.equity.service.impl;

import com.example.equity.calculation.SmartEquityCalculator;
import com.example.equity.model.Company;
import com.example.equity.model.HoldingResult;
import com.example.equity.repository.CompanyRepository;
import com.example.equity.repository.HoldingResultRepository;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class JdbcDatabaseSaver implements SmartEquityCalculator.DatabaseSaver {

    private final CompanyRepository companyRepository;
    private final HoldingResultRepository holdingResultRepository;
    
    public JdbcDatabaseSaver(
            CompanyRepository companyRepository,
            HoldingResultRepository holdingResultRepository) {
        this.companyRepository = companyRepository;
        this.holdingResultRepository = holdingResultRepository;
    }
    
    @Override
    @Transactional
    public void saveResults(List<SmartEquityCalculator.HoldingResult> results) {
        // 1. 批量获取公司
        Set<Long> companyIds = new HashSet<>();
        results.forEach(r -> {
            companyIds.add(r.getParentId());
            companyIds.add(r.getChildId());
        });
        
        Map<Long, Company> companyMap = companyRepository.findAllById(companyIds).stream()
                .collect(Collectors.toMap(Company::getId, c -> c));
        
        // 2. 转换为实体
        List<HoldingResult> entities = results.stream()
            .filter(r -> companyMap.containsKey(r.getParentId()) && companyMap.containsKey(r.getChildId()))
            .map(r -> new HoldingResult(
                companyMap.get(r.getParentId()),
                companyMap.get(r.getChildId()),
                r.getRatio()
            ))
            .collect(Collectors.toList());
        
        // 3. 保存
        holdingResultRepository.saveAll(entities);
    }
}
