package com.example.equity.service.impl;

import com.example.equity.calculation.EquityCalculator;
import com.example.equity.calculation.SmartEquityCalculator;
import com.example.equity.model.*;
import com.example.equity.repository.*;
import com.example.equity.service.EquityService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EquityServiceImpl implements EquityService {

    private final CompanyRepository companyRepository;
    private final EquityRelationshipRepository equityRelationshipRepository;
    private final HoldingResultRepository holdingResultRepository;
    private final SmartEquityCalculator smartEquityCalculator;
    
    public EquityServiceImpl(
            CompanyRepository companyRepository,
            EquityRelationshipRepository equityRelationshipRepository,
            HoldingResultRepository holdingResultRepository,
            SmartEquityCalculator smartEquityCalculator) {
        this.companyRepository = companyRepository;
        this.equityRelationshipRepository = equityRelationshipRepository;
        this.holdingResultRepository = holdingResultRepository;
        this.smartEquityCalculator = smartEquityCalculator;
    }
    
    @Override
    @Transactional
    public int updateEquityRelationships(List<EquityChangeRequest> changes) {
        // 1. 准备变更数据
        List<EquityCalculator.EquityRelationship> added = new ArrayList<>();
        List<EquityCalculator.EquityRelationship> updated = new ArrayList<>();
        List<EquityCalculator.EquityRelationship> deleted = new ArrayList<>();
        
        for (EquityChangeRequest change : changes) {
            EquityRelationship er = change.getRelationship();
            switch (change.getAction()) {
                case ADD:
                    equityRelationshipRepository.save(er);
                    added.add(toCalculationModel(er));
                    break;
                case UPDATE:
                    equityRelationshipRepository.save(er);
                    updated.add(toCalculationModel(er));
                    break;
                case DELETE:
                    equityRelationshipRepository.delete(er);
                    deleted.add(toCalculationModel(er));
                    break;
            }
        }
        
        // 2. 触发异步计算
        return smartEquityCalculator.handleChanges(added, updated, deleted);
    }
    
    @Override
    public BigDecimal getIndirectHolding(Long parentId, Long childId) {
        return holdingResultRepository.findByParentAndChild(parentId, childId)
                .map(HoldingResult::getIndirectRatio)
                .orElse(BigDecimal.ZERO);
    }
    
    @Override
    public List<Company> getControlledCompanies(Long companyId, BigDecimal threshold) {
        final BigDecimal finalThreshold = threshold != null ? threshold : new BigDecimal("0.5");

        return holdingResultRepository.findAll().stream()
                .filter(hr -> hr.getParent().getId().equals(companyId) &&
                        hr.getIndirectRatio().compareTo(finalThreshold) >= 0)
                .map(HoldingResult::getChild)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public int performFullCalculation() {
        // 执行全量计算
        return smartEquityCalculator.performFullCalculation();
    }
    
    // 转换为计算引擎需要的模型
    private EquityCalculator.EquityRelationship toCalculationModel(EquityRelationship er) {
        return new EquityCalculator.EquityRelationship(
            er.getParentId(), 
            er.getChildId(), 
            er.getRatio()
        );
    }
}
