package com.example.equity.config;

import com.example.equity.calculation.EquityCalculator;
import com.example.equity.calculation.SmartEquityCalculator;
import com.example.equity.model.EquityRelationship;
import com.example.equity.repository.EquityRelationshipRepository;
import com.example.equity.service.impl.JdbcDatabaseSaver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.stream.Collectors;

@Configuration
public class EquityCalculatorConfig {

    @Bean
    public SmartEquityCalculator smartEquityCalculator(
            EquityRelationshipRepository equityRelationshipRepository,
            JdbcDatabaseSaver databaseSaver) {
        
        // 获取当前股权关系
        List<EquityRelationship> currentRelationships = equityRelationshipRepository.findCurrent();
        
        // 转换为计算引擎需要的格式
        List<EquityCalculator.EquityRelationship> calculationRelationships = 
            currentRelationships.stream()
                .map(er -> new EquityCalculator.EquityRelationship(
                    er.getParentId(), 
                    er.getChildId(), 
                    er.getRatio()
                ))
                .collect(Collectors.toList());
        
        // 创建智能计算引擎（最大深度设为10）
        return new SmartEquityCalculator(calculationRelationships, 10, databaseSaver);
    }
}
