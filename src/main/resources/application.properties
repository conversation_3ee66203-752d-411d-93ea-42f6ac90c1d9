# H2 Database Configuration
spring.datasource.url=jdbc:h2:mem:equitydb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 Console (for development)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# SQL Initialization
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
spring.sql.init.data-locations=classpath:data.sql

# Server Configuration
server.port=8080

# Logging
logging.level.com.example.equity=DEBUG
logging.level.org.springframework.jdbc=DEBUG

# Performance Test Configuration
# Set to true to run performance tests on startup
equity.performance.test.enabled=false
