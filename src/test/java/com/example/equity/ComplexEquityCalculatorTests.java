package com.example.equity;

import com.example.equity.calculation.EquityCalculator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.Timeout;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@TestInstance(TestInstance.Lifecycle.PER_METHOD)
class ComplexEquityCalculatorTests {

    private EquityCalculator calculator;
    private List<EquityCalculator.EquityRelationship> relationships;
    private Map<String, Long> companyIdMap;
    private Random random = new Random(42); // 固定随机种子，确保测试可重现

    @BeforeEach
    void setUp() {
        // 生成1000家公司的复杂架构
        relationships = generateComplexCorporateStructure();
        calculator = new EquityCalculator(relationships, 15);
        companyIdMap = buildCompanyIdMap(relationships);
    }

    /**
     * 生成1000家公司的复杂股权架构
     */
    private List<EquityCalculator.EquityRelationship> generateComplexCorporateStructure() {
        List<EquityCalculator.EquityRelationship> relationships = new ArrayList<>();
        Map<String, Long> idMap = new HashMap<>();
        long nextId = 1;
        
        // 1. 集团总部 (1)
        idMap.put("GROUP", nextId++);
        
        // 2. 区域总部 (10)
        for (int i = 1; i <= 10; i++) {
            idMap.put("REGION_" + i, nextId++);
        }
        
        // 3. 省级公司 (100)
        for (int i = 1; i <= 100; i++) {
            idMap.put("PROV_" + i, nextId++);
        }
        
        // 4. 市级公司 (500)
        for (int i = 1; i <= 500; i++) {
            idMap.put("CITY_" + i, nextId++);
        }
        
        // 5. 基层公司 (300)
        for (int i = 1; i <= 300; i++) {
            idMap.put("GRASS_" + i, nextId++);
        }
        
        // 6. 专业子公司 (5)
        for (int i = 1; i <= 5; i++) {
            idMap.put("SPEC_" + i, nextId++);
        }
        
        // 7. 二级专业公司 (10)
        for (int i = 1; i <= 10; i++) {
            idMap.put("SPEC2_" + i, nextId++);
        }
        
        // 8. 三级专业公司 (10)
        for (int i = 1; i <= 10; i++) {
            idMap.put("SPEC3_" + i, nextId++);
        }
        
        // 9. 金融控股平台 (1)
        idMap.put("FIN_HLDG", nextId++);
        
        // 10. 保险公司 (5)
        for (int i = 1; i <= 5; i++) {
            idMap.put("INSUR_" + i, nextId++);
        }
        
        // 11. 银行 (5)
        for (int i = 1; i <= 5; i++) {
            idMap.put("BANK_" + i, nextId++);
        }
        
        // 12. 证券公司 (5)
        for (int i = 1; i <= 5; i++) {
            idMap.put("SECUR_" + i, nextId++);
        }
        
        // 验证总公司数
        assertEquals(1001, idMap.size(), "应该有1001家公司");
        
        // 1. 集团总部 → 区域总部 (100%)
        for (int i = 1; i <= 10; i++) {
            relationships.add(new EquityCalculator.EquityRelationship(
                idMap.get("GROUP"), idMap.get("REGION_" + i), new BigDecimal("1.0")));
        }
        
        // 2. 区域总部 → 省级公司 (40%-85%)
        for (int region = 1; region <= 10; region++) {
            for (int prov = (region-1)*10 + 1; prov <= region*10; prov++) {
                BigDecimal ratio = new BigDecimal(String.format("%.4f", 0.4 + (region * 0.045)));
                relationships.add(new EquityCalculator.EquityRelationship(
                    idMap.get("REGION_" + region), idMap.get("PROV_" + prov), ratio));
            }
        }
        
        // 3. 省级公司 → 市级公司 (55%-75%)
        for (int prov = 1; prov <= 100; prov++) {
            for (int city = (prov-1)*5 + 1; city <= prov*5; city++) {
                if (city > 500) break;
                
                BigDecimal ratio = new BigDecimal(String.format("%.4f", 0.55 + (prov % 10) * 0.02));
                relationships.add(new EquityCalculator.EquityRelationship(
                    idMap.get("PROV_" + prov), idMap.get("CITY_" + city), ratio));
            }
        }
        
        // 4. 市级公司 → 基层公司 (55%-67%)
        for (int city = 1; city <= 500; city++) {
            for (int grass = (city-1)*3 + 1; grass <= (city-1)*3 + 3; grass++) {
                if (grass > 300) break;
                
                BigDecimal ratio = new BigDecimal(String.format("%.4f", 0.55 + (city % 5) * 0.024));
                relationships.add(new EquityCalculator.EquityRelationship(
                    idMap.get("CITY_" + city), idMap.get("GRASS_" + grass), ratio));
            }
        }
        
        // 5. 集团总部 → 专业子公司 (35%-75%)
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GROUP"), idMap.get("SPEC_1"), new BigDecimal("0.55")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GROUP"), idMap.get("SPEC_2"), new BigDecimal("0.62")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GROUP"), idMap.get("SPEC_3"), new BigDecimal("0.75")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GROUP"), idMap.get("SPEC_4"), new BigDecimal("0.48")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GROUP"), idMap.get("SPEC_5"), new BigDecimal("0.35")));
        
        // 6. 专业子公司 → 二级专业公司 (55%-65%)
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_1"), idMap.get("SPEC2_1"), new BigDecimal("0.65")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_1"), idMap.get("SPEC2_2"), new BigDecimal("0.55")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_2"), idMap.get("SPEC2_3"), new BigDecimal("0.60")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_2"), idMap.get("SPEC2_4"), new BigDecimal("0.58")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_3"), idMap.get("SPEC2_5"), new BigDecimal("0.63")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_3"), idMap.get("SPEC2_6"), new BigDecimal("0.61")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_4"), idMap.get("SPEC2_7"), new BigDecimal("0.57")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_4"), idMap.get("SPEC2_8"), new BigDecimal("0.54")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_5"), idMap.get("SPEC2_9"), new BigDecimal("0.59")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_5"), idMap.get("SPEC2_10"), new BigDecimal("0.56")));
        
        // 7. 二级专业公司 → 三级专业公司 (60%-70%)
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_1"), idMap.get("SPEC3_1"), new BigDecimal("0.70")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_1"), idMap.get("SPEC3_2"), new BigDecimal("0.60")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_2"), idMap.get("SPEC3_3"), new BigDecimal("0.65")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_2"), idMap.get("SPEC3_4"), new BigDecimal("0.62")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_3"), idMap.get("SPEC3_5"), new BigDecimal("0.68")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_3"), idMap.get("SPEC3_6"), new BigDecimal("0.64")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_4"), idMap.get("SPEC3_7"), new BigDecimal("0.66")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_4"), idMap.get("SPEC3_8"), new BigDecimal("0.63")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_5"), idMap.get("SPEC3_9"), new BigDecimal("0.69")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC2_5"), idMap.get("SPEC3_10"), new BigDecimal("0.61")));
        
        // 8. 金融控股平台结构
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GROUP"), idMap.get("FIN_HLDG"), new BigDecimal("1.0")));
        
        // 8.1 金融控股平台 → 保险公司 (70%-90%)
        for (int i = 1; i <= 5; i++) {
            BigDecimal ratio = new BigDecimal(String.format("%.4f", 0.9 - (i-1)*0.05));
            relationships.add(new EquityCalculator.EquityRelationship(
                idMap.get("FIN_HLDG"), idMap.get("INSUR_" + i), ratio));
        }
        
        // 8.2 金融控股平台 → 银行 (65%-85%)
        for (int i = 1; i <= 5; i++) {
            BigDecimal ratio = new BigDecimal(String.format("%.4f", 0.85 - (i-1)*0.05));
            relationships.add(new EquityCalculator.EquityRelationship(
                idMap.get("FIN_HLDG"), idMap.get("BANK_" + i), ratio));
        }
        
        // 8.3 金融控股平台 → 证券公司 (70%-90%)
        for (int i = 1; i <= 5; i++) {
            BigDecimal ratio = new BigDecimal(String.format("%.4f", 0.9 - (i-1)*0.05));
            relationships.add(new EquityCalculator.EquityRelationship(
                idMap.get("FIN_HLDG"), idMap.get("SECUR_" + i), ratio));
        }
        
        // 9. 多路径持股案例
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_1"), idMap.get("PROV_1"), new BigDecimal("0.30")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SPEC_1"), idMap.get("CITY_1"), new BigDecimal("0.40")));
        
        // 10. 循环持股案例1：集团←区域←省级←集团
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("REGION_1"), idMap.get("GROUP"), new BigDecimal("0.15")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("PROV_1"), idMap.get("REGION_1"), new BigDecimal("0.25")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GROUP"), idMap.get("PROV_1"), new BigDecimal("0.10")));
        
        // 11. 循环持股案例2：金融控股←保险←证券←金融控股
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("INSUR_1"), idMap.get("FIN_HLDG"), new BigDecimal("0.05")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("SECUR_1"), idMap.get("INSUR_1"), new BigDecimal("0.08")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("FIN_HLDG"), idMap.get("SECUR_1"), new BigDecimal("0.03")));
        
        // 12. 循环持股案例3：省级←市级←基层←省级
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("CITY_1"), idMap.get("PROV_1"), new BigDecimal("0.12")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("GRASS_1"), idMap.get("CITY_1"), new BigDecimal("0.07")));
        relationships.add(new EquityCalculator.EquityRelationship(
            idMap.get("PROV_1"), idMap.get("GRASS_1"), new BigDecimal("0.05")));
        
        // 13. 添加更多复杂关系
        relationships.addAll(generateComplexRelationships(idMap));
        
        return relationships;
    }
    
    /**
     * 生成额外的复杂关系（交叉持股、一致行动人等）
     */
    private List<EquityCalculator.EquityRelationship> generateComplexRelationships(Map<String, Long> idMap) {
        List<EquityCalculator.EquityRelationship> complexRelationships = new ArrayList<>();
        List<Long> allCompanyIds = new ArrayList<>(idMap.values());
        
        // 1. 添加跨层级持股（200条）
        for (int i = 0; i < 200; i++) {
            Long parent = allCompanyIds.get(random.nextInt(allCompanyIds.size()));
            Long child = allCompanyIds.get(random.nextInt(allCompanyIds.size()));
            
            // 确保不是同一公司
            if (!parent.equals(child)) {
                // 生成合理的持股比例
                double ratio;
                double rand = random.nextDouble();
                if (rand < 0.6) {
                    ratio = 0.5 + random.nextDouble() * 0.5; // 50%-100%
                } else if (rand < 0.9) {
                    ratio = 0.2 + random.nextDouble() * 0.3; // 20%-50%
                } else {
                    ratio = random.nextDouble() * 0.2; // 0%-20%
                }
                
                // 检查是否已存在关系
                boolean exists = complexRelationships.stream()
                    .anyMatch(r -> r.getParentId().equals(parent) && r.getChildId().equals(child));
                
                if (!exists) {
                    complexRelationships.add(new EquityCalculator.EquityRelationship(
                        parent, child, new BigDecimal(String.format("%.4f", ratio))));
                }
            }
        }
        
        // 2. 添加一致行动人关系（100条）
        for (int i = 0; i < 100; i++) {
            // 选择一个目标公司
            Long target = allCompanyIds.get(random.nextInt(allCompanyIds.size()));
            
            // 选择2-3个一致行动人
            Set<Long> actors = new HashSet<>();
            while (actors.size() < random.nextInt(2) + 2) {
                Long actor = allCompanyIds.get(random.nextInt(allCompanyIds.size()));
                if (!actor.equals(target)) {
                    actors.add(actor);
                }
            }
            
            // 为每个一致行动人创建关系
            for (Long actor : actors) {
                boolean exists = complexRelationships.stream()
                    .anyMatch(r -> r.getParentId().equals(actor) && r.getChildId().equals(target));
                
                if (!exists) {
                    double ratio = 0.2 + random.nextDouble() * 0.3; // 20%-50%
                    complexRelationships.add(new EquityCalculator.EquityRelationship(
                        actor, target, new BigDecimal(String.format("%.4f", ratio))));
                }
            }
        }
        
        // 3. 添加表决权委托（50条）
        for (int i = 0; i < 50; i++) {
            Long entruster = allCompanyIds.get(random.nextInt(allCompanyIds.size()));
            Long trustee = allCompanyIds.get(random.nextInt(allCompanyIds.size()));
            Long target = allCompanyIds.get(random.nextInt(allCompanyIds.size()));
            
            if (!entruster.equals(trustee) && !entruster.equals(target) && !trustee.equals(target)) {
                // 委托比例为委托方直接持股的50-100%
                double directRatio = 0.5 + random.nextDouble() * 0.5;
                double entrustRatio = directRatio * (0.5 + random.nextDouble() * 0.5);
                
                // 创建委托方到目标公司的关系
                boolean exists = complexRelationships.stream()
                    .anyMatch(r -> r.getParentId().equals(entruster) && r.getChildId().equals(target));
                
                if (!exists) {
                    complexRelationships.add(new EquityCalculator.EquityRelationship(
                        entruster, target, new BigDecimal(String.format("%.4f", directRatio))));
                }
                
                // 创建受托方到目标公司的关系（增加委托部分）
                boolean trusteeExists = complexRelationships.stream()
                    .anyMatch(r -> r.getParentId().equals(trustee) && r.getChildId().equals(target));
                
                if (trusteeExists) {
                    // 如果已有关系，增加委托比例
                    for (EquityCalculator.EquityRelationship rel : complexRelationships) {
                        if (rel.getParentId().equals(trustee) && rel.getChildId().equals(target)) {
                            BigDecimal newRatio = rel.getRatio()
                                .add(new BigDecimal(String.format("%.4f", entrustRatio)))
                                .min(BigDecimal.ONE);
                            complexRelationships.remove(rel);
                            complexRelationships.add(new EquityCalculator.EquityRelationship(
                                trustee, target, newRatio));
                            break;
                        }
                    }
                } else {
                    complexRelationships.add(new EquityCalculator.EquityRelationship(
                        trustee, target, new BigDecimal(String.format("%.4f", entrustRatio))));
                }
            }
        }
        
        return complexRelationships;
    }
    
    private Map<String, Long> buildCompanyIdMap(List<EquityCalculator.EquityRelationship> relationships) {
        Map<String, Long> map = new HashMap<>();
        
        // 添加所有可能的公司ID
        relationships.forEach(rel -> {
            map.putIfAbsent("GROUP", 1L);
            for (int i = 1; i <= 10; i++) map.putIfAbsent("REGION_" + i, (long)(1 + i));
            for (int i = 1; i <= 100; i++) map.putIfAbsent("PROV_" + i, (long)(11 + i));
            for (int i = 1; i <= 500; i++) map.putIfAbsent("CITY_" + i, (long)(111 + i));
            for (int i = 1; i <= 300; i++) map.putIfAbsent("GRASS_" + i, (long)(611 + i));
            for (int i = 1; i <= 5; i++) map.putIfAbsent("SPEC_" + i, (long)(911 + i));
            for (int i = 1; i <= 10; i++) map.putIfAbsent("SPEC2_" + i, (long)(916 + i));
            for (int i = 1; i <= 10; i++) map.putIfAbsent("SPEC3_" + i, (long)(926 + i));
            map.putIfAbsent("FIN_HLDG", 936L);
            for (int i = 1; i <= 5; i++) map.putIfAbsent("INSUR_" + i, (long)(937 + i));
            for (int i = 1; i <= 5; i++) map.putIfAbsent("BANK_" + i, (long)(942 + i));
            for (int i = 1; i <= 5; i++) map.putIfAbsent("SECUR_" + i, (long)(947 + i));
        });
        
        return map;
    }
    
    // ====================== 测试用例 ======================
    
    @Test
    @Timeout(5000)
    void testStandardIndirectHolding() {
        // 集团总部(1) → 区域总部1(2) → 省级公司1(11) → 市级公司1(111) → 基层公司1(611)
        // 持股比例: 100% → 85% → 75% → 67%
        // 预期结果: 1.0 * 0.85 * 0.75 * 0.67 = 0.427125
        
        BigDecimal result = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("GRASS_1"));
        
        assertEquals(new BigDecimal("0.427125"), result, 
            "标准间接持股计算错误");
    }
    
    @Test
    @Timeout(5000)
    void testMultiPathIndirectHolding() {
        // 路径1: 集团总部 → 省级公司1 → 市级公司1
        // 1.0 * 0.85 * 0.75 = 0.6375
        
        // 路径2: 集团总部 → 专业子公司1 → 市级公司1
        // 0.55 * 0.40 = 0.22
        
        // 预期结果: 0.6375 + 0.22 = 0.8575
        
        BigDecimal result = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("CITY_1"));
        
        assertEquals(new BigDecimal("0.8575"), result, 
            "多路径间接持股计算错误");
    }
    
    @Test
    @Timeout(5000)
    void testCriticalThreshold() {
        // 集团总部对专业子公司4的持股比例为48%
        // 预期结果: 0.48 (应小于控制阈值0.5)
        
        BigDecimal result = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("SPEC_4"));
        
        assertEquals(new BigDecimal("0.48"), result, 
            "临界阈值计算错误");
        assertTrue(result.compareTo(new BigDecimal("0.5")) < 0,
            "48%不应达到控制阈值");
    }
    
    @Test
    @Timeout(5000)
    void testSmallHolding() {
        // 测试非常小的持股比例
        BigDecimal result = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("SPEC3_10"));
        
        // 预期: 0.55 * 0.65 * 0.61 = 0.217475
        assertEquals(new BigDecimal("0.217475"), result, 
            "小比例持股计算错误");
        
        // 添加一个非常小的持股比例测试
        Long smallHolder = companyIdMap.get("INSUR_5");
        Long smallTarget = companyIdMap.get("BANK_5");
        
        // 直接持股0.1%
        relationships.add(new EquityCalculator.EquityRelationship(
            smallHolder, smallTarget, new BigDecimal("0.001")));
        
        // 创建新的计算引擎（测试需要）
        EquityCalculator smallHoldingCalculator = 
            new EquityCalculator(relationships, 15);
        
        BigDecimal smallResult = smallHoldingCalculator.calculateIndirectHolding(
            smallHolder, smallTarget);
        
        assertEquals(new BigDecimal("0.001"), smallResult, 
            "非常小的持股比例计算错误");
    }
    
    @Test
    @Timeout(5000)
    void testNoRelationship() {
        // 测试无持股关系的公司
        Long unrelated1 = companyIdMap.get("GRASS_300");
        Long unrelated2 = companyIdMap.get("SPEC3_10");
        
        BigDecimal result = calculator.calculateIndirectHolding(unrelated1, unrelated2);
        assertEquals(BigDecimal.ZERO, result, 
            "无持股关系应返回0");
    }
    
    @Test
    @Timeout(5000)
    void testCircularOwnershipDetection() {
        // 测试循环持股检测
        assertTrue(calculator.hasCircularOwnership(companyIdMap.get("GROUP")),
            "应检测到集团总部的循环持股");
        assertTrue(calculator.hasCircularOwnership(companyIdMap.get("REGION_1")),
            "应检测到区域总部1的循环持股");
        assertTrue(calculator.hasCircularOwnership(companyIdMap.get("PROV_1")),
            "应检测到省级公司1的循环持股");
        
        // 测试非循环公司
        assertFalse(calculator.hasCircularOwnership(companyIdMap.get("GRASS_1")),
            "基层公司1不应有循环持股");
        assertFalse(calculator.hasCircularOwnership(companyIdMap.get("SPEC_1")),
            "专业子公司1不应有循环持股");
    }
    
    @Test
    @Timeout(5000)
    void testCircularOwnershipCalculation() {
        // 测试循环持股计算结果
        BigDecimal groupToItself = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("GROUP"));
        
        // 由于循环，结果应小于1.0
        assertTrue(groupToItself.compareTo(BigDecimal.ONE) < 0,
            "循环持股计算结果不应达到100%");
        assertFalse(groupToItself.compareTo(BigDecimal.ZERO) == 0,
            "循环持股计算结果不应为0");
        
        // 验证金融板块循环
        BigDecimal finHoldingToItself = calculator.calculateIndirectHolding(
            companyIdMap.get("FIN_HLDG"), 
            companyIdMap.get("FIN_HLDG"));
        
        assertTrue(finHoldingToItself.compareTo(BigDecimal.ONE) < 0,
            "金融控股平台循环持股计算结果不应达到100%");
    }
    
    @Test
    @Timeout(5000)
    void testDeepPathCalculation() {
        // 测试深度路径：集团 → 专业子公司 → 二级专业公司 → 三级专业公司
        // 预期: 0.55 * 0.65 * 0.70 = 0.25025
        
        BigDecimal result = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("SPEC3_1"));
        
        assertEquals(new BigDecimal("0.25025"), result, 
            "深度路径持股计算错误");
    }
    
    @Test
    @Timeout(5000)
    void testPerformanceWith1000Companies() {
        // 测试1000家公司规模下的性能
        List<Long> allCompanyIds = new ArrayList<>(companyIdMap.values());
        Collections.shuffle(allCompanyIds, new Random(42)); // 固定随机种子
        
        // 测试100次随机计算
        long start = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            Long from = allCompanyIds.get(i);
            Long to = allCompanyIds.get((i + 50) % allCompanyIds.size());
            
            // 确保不是同一公司
            if (from.equals(to)) {
                to = allCompanyIds.get((i + 51) % allCompanyIds.size());
            }
            
            calculator.calculateIndirectHolding(from, to);
        }
        
        long time = System.currentTimeMillis() - start;
        System.out.println("100次随机计算耗时: " + time + "ms");
        
        // 性能要求：100次计算应在2000ms内
        assertTrue(time < 2000, 
            "性能不达标: 100次计算耗时" + time + "ms (应<2000ms)");
        
        // 计算平均时间
        double avgTime = (double) time / 100;
        System.out.println("平均计算时间: " + String.format("%.2f", avgTime) + "ms/次");
        assertTrue(avgTime < 20, 
            "平均计算时间过高: " + String.format("%.2f", avgTime) + "ms/次 (应<20ms)");
    }
    
    @Test
    @Timeout(10000)
    void testIncrementalCalculation() {
        // 1. 获取原始计算结果
        BigDecimal original = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("GRASS_1"));
        
        // 2. 修改关系：区域总部1对省级公司1的持股从85%改为90%
        Long region1 = companyIdMap.get("REGION_1");
        Long prov1 = companyIdMap.get("PROV_1");
        
        // 创建修改后的关系
        List<EquityCalculator.EquityRelationship> modifiedRelationships = new ArrayList<>(relationships);
        modifiedRelationships.removeIf(r -> 
            r.getParentId().equals(region1) && r.getChildId().equals(prov1));
        modifiedRelationships.add(new EquityCalculator.EquityRelationship(
            region1, prov1, new BigDecimal("0.90")));
        
        // 3. 创建新的计算引擎
        EquityCalculator updatedCalculator = 
            new EquityCalculator(modifiedRelationships, 15);
        
        // 4. 验证计算结果
        BigDecimal updated = updatedCalculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("GRASS_1"));
        
        // 预期: 1.0 * 0.90 * 0.75 * 0.67 = 0.45225
        assertEquals(new BigDecimal("0.45225"), updated, 
            "增量计算结果错误");
        
        // 验证变化量
        BigDecimal diff = updated.subtract(original);
        assertEquals(new BigDecimal("0.025125"), diff, 
            "变化量计算错误");
    }
    
    @Test
    @Timeout(5000)
    void testEdgeCases() {
        // 1. 测试自身持股
        BigDecimal selfHolding = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("GROUP"));
        assertTrue(selfHolding.compareTo(BigDecimal.ONE) <= 0,
            "自身持股不应超过100%");
        
        // 2. 测试100%持股
        BigDecimal fullHolding = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("REGION_1"));
        assertEquals(BigDecimal.ONE, fullHolding,
            "100%持股计算错误");
        
        // 3. 测试0.001%持股
        Long smallHolder = companyIdMap.get("INSUR_5");
        Long smallTarget = companyIdMap.get("BANK_5");
        
        // 添加0.001%持股
        relationships.add(new EquityCalculator.EquityRelationship(
            smallHolder, smallTarget, new BigDecimal("0.00001")));
        
        EquityCalculator smallCalculator = 
            new EquityCalculator(relationships, 15);
        
        BigDecimal tinyHolding = smallCalculator.calculateIndirectHolding(
            smallHolder, smallTarget);
        assertEquals(new BigDecimal("0.00001"), tinyHolding,
            "极小持股比例计算错误");
        
        // 4. 测试最大深度限制
        // 创建一个超过最大深度的路径
        Long start = companyIdMap.get("GROUP");
        Long current = start;
        List<EquityCalculator.EquityRelationship> deepPath = new ArrayList<>(relationships);
        
        for (int i = 1; i <= 20; i++) {
            Long next = 10000L + i; // 使用新ID
            deepPath.add(new EquityCalculator.EquityRelationship(current, next, new BigDecimal("0.5")));
            current = next;
        }
        
        EquityCalculator deepCalculator = 
            new EquityCalculator(deepPath, 15); // 最大深度15
        
        BigDecimal deepResult = deepCalculator.calculateIndirectHolding(
            start, 10000L + 20);
        
        // 由于深度限制为15，结果应为0.5^15
        BigDecimal expected = new BigDecimal("0.000030517578125");
        assertEquals(expected, deepResult,
            "最大深度限制未生效");
    }
    
    @Test
    @Timeout(10000)
    void testConsistencyAfterMultipleChanges() {
        // 1. 获取基准结果
        BigDecimal baseResult = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("GRASS_1"));
        
        // 2. 应用多个变更
        List<EquityCalculator.EquityRelationship> modifiedRelationships = new ArrayList<>(relationships);
        
        // 变更1: 区域总部1对省级公司1的持股从85%改为90%
        modifiedRelationships.removeIf(r -> 
            r.getParentId().equals(companyIdMap.get("REGION_1")) && 
            r.getChildId().equals(companyIdMap.get("PROV_1")));
        modifiedRelationships.add(new EquityCalculator.EquityRelationship(
            companyIdMap.get("REGION_1"), 
            companyIdMap.get("PROV_1"), 
            new BigDecimal("0.90")));
        
        // 变更2: 添加专业子公司1对市级公司1的额外持股
        modifiedRelationships.add(new EquityCalculator.EquityRelationship(
            companyIdMap.get("SPEC_1"), 
            companyIdMap.get("CITY_1"), 
            new BigDecimal("0.10")));
        
        // 变更3: 修改循环持股比例
        modifiedRelationships.removeIf(r -> 
            r.getParentId().equals(companyIdMap.get("REGION_1")) && 
            r.getChildId().equals(companyIdMap.get("GROUP")));
        modifiedRelationships.add(new EquityCalculator.EquityRelationship(
            companyIdMap.get("REGION_1"), 
            companyIdMap.get("GROUP"), 
            new BigDecimal("0.20")));
        
        // 3. 创建新的计算引擎
        EquityCalculator updatedCalculator = 
            new EquityCalculator(modifiedRelationships, 15);
        
        // 4. 验证计算结果
        BigDecimal updatedResult = updatedCalculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("GRASS_1"));
        
        // 预期变化:
        // 1. 区域路径: (0.90-0.85)*0.75*0.67 = 0.025125
        // 2. 专业子公司路径: 0.10 * 0.67 = 0.067
        // 总变化: 0.025125 + 0.067 = 0.092125
        BigDecimal expectedChange = new BigDecimal("0.092125");
        BigDecimal actualChange = updatedResult.subtract(baseResult);
        
        assertEquals(expectedChange, actualChange, 
            "多次变更后计算结果不一致");
    }
    
    @Test
    @Timeout(15000)
    void testFullCalculationPerformance() {
        // 测试全量计算性能（样本测试）
        List<Long> allCompanyIds = new ArrayList<>(companyIdMap.values());
        Collections.shuffle(allCompanyIds, new Random(42));
        
        // 只测试部分样本（100x100）
        int sampleSize = Math.min(100, allCompanyIds.size());
        long start = System.currentTimeMillis();
        
        for (int i = 0; i < sampleSize; i++) {
            for (int j = 0; j < sampleSize; j++) {
                if (i != j) {
                    calculator.calculateIndirectHolding(
                        allCompanyIds.get(i), 
                        allCompanyIds.get(j));
                }
            }
        }
        
        long time = System.currentTimeMillis() - start;
        System.out.println("样本全量计算(" + sampleSize + "x" + sampleSize + "): " + time + "ms");
        
        // 估算1000x1000计算时间
        long estimatedTotal = (long)(time * 1000.0 * 1000.0 / (sampleSize * sampleSize));
        System.out.println("预估全量计算(1000x1000): " + formatTime(estimatedTotal));
        
        // 性能要求：预估时间应小于5分钟
        assertTrue(estimatedTotal < 300_000, 
            "预估全量计算时间过长: " + formatTime(estimatedTotal) + " (应<5分钟)");
    }
    
    private String formatTime(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + "ms";
        } else if (milliseconds < 60_000) {
            return String.format("%.2f秒", milliseconds / 1000.0);
        } else if (milliseconds < 3_600_000) {
            return String.format("%.2f分钟", milliseconds / 60_000.0);
        } else {
            return String.format("%.2f小时", milliseconds / 3_600_000.0);
        }
    }
    
    @Test
    @Timeout(5000)
    void testFinancialSectorCircularHolding() {
        // 测试金融板块循环持股
        // 金融控股平台 ←5%─ 保险公司1 ←8%─ 证券公司1 ←3%─ 金融控股平台
        
        // 1. 金融控股平台对自身的间接持股
        BigDecimal finHoldingToSelf = calculator.calculateIndirectHolding(
            companyIdMap.get("FIN_HLDG"), 
            companyIdMap.get("FIN_HLDG"));
        
        // 库藏股法计算：1 - 0.05*0.08*0.03 = 0.99988
        BigDecimal expected = new BigDecimal("0.99988");
        assertTrue(finHoldingToSelf.compareTo(expected) <= 0,
            "金融控股平台循环持股计算错误");
        assertTrue(finHoldingToSelf.compareTo(BigDecimal.ZERO) > 0,
            "金融控股平台循环持股不应为0");
        
        // 2. 保险公司1对证券公司1的间接持股
        BigDecimal insurToSecur = calculator.calculateIndirectHolding(
            companyIdMap.get("INSUR_1"), 
            companyIdMap.get("SECUR_1"));
        
        // 直接持股 + 间接持股 (通过金融控股平台)
        // 0.08 + 0.05 * 0.03 = 0.0815
        assertEquals(new BigDecimal("0.0815"), insurToSecur,
            "保险公司对证券公司的持股计算错误");
    }
    
    @Test
    @Timeout(5000)
    void testRegionalCircularHolding() {
        // 测试区域层级循环持股
        // 集团总部 ←10%─ 省级公司1 ←25%─ 区域总部1 ←15%─ 集团总部
        
        // 1. 集团总部对省级公司1的间接持股
        BigDecimal groupToProv = calculator.calculateIndirectHolding(
            companyIdMap.get("GROUP"), 
            companyIdMap.get("PROV_1"));
        
        // 标准路径: 0.85
        // 循环影响: 库藏股法调整
        assertTrue(groupToProv.compareTo(new BigDecimal("0.85")) < 0,
            "循环持股应降低集团对省级公司的持股比例");
        assertTrue(groupToProv.compareTo(new BigDecimal("0.8")) > 0,
            "集团对省级公司的持股比例应在0.8-0.85之间");
        
        // 2. 省级公司1对集团总部的间接持股
        BigDecimal provToGroup = calculator.calculateIndirectHolding(
            companyIdMap.get("PROV_1"), 
            companyIdMap.get("GROUP"));
        
        // 0.25 * 0.15 * (1 - 循环因子)
        assertTrue(provToGroup.compareTo(BigDecimal.ZERO) > 0,
            "省级公司对集团的间接持股不应为0");
        assertTrue(provToGroup.compareTo(new BigDecimal("0.04")) < 0,
            "省级公司对集团的间接持股应小于4%");
    }
}