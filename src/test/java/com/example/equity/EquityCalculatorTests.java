package com.example.equity;

import com.example.equity.calculation.EquityCalculator;
import com.example.equity.model.Company;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@SpringBootTest
class EquityCalculatorTests {

    @Test
    void testIndirectHoldingCalculation() {
        // 创建测试数据
        Company group = new Company("集团总部", "GROUP");
        Company subA = new Company("子公司A", "SUB_A");
        Company subB = new Company("子公司B", "SUB_B");
        Company subC = new Company("子公司C", "SUB_C");

        // 设置ID（H2内存数据库会自动分配，这里手动设置用于测试）
        group.setId(1L);
        subA.setId(2L);
        subB.setId(3L);
        subC.setId(4L);

        // 创建股权关系
        List<EquityCalculator.EquityRelationship> relationships = Arrays.asList(
                new EquityCalculator.EquityRelationship(1L, 2L, new BigDecimal("0.6")), // 集团→A (60%)
                new EquityCalculator.EquityRelationship(2L, 3L, new BigDecimal("0.5")), // A→B (50%)
                new EquityCalculator.EquityRelationship(1L, 4L, new BigDecimal("0.4")), // 集团→C (40%)
                new EquityCalculator.EquityRelationship(4L, 3L, new BigDecimal("0.3")) // C→B (30%)
        );

        // 创建计算引擎
        EquityCalculator calculator = new EquityCalculator(relationships, 10);

        // 测试计算
        BigDecimal groupToB = calculator.calculateIndirectHolding(1L, 3L);
        assertEquals(new BigDecimal("0.42"), groupToB);

        BigDecimal groupToC = calculator.calculateIndirectHolding(1L, 4L);
        assertEquals(new BigDecimal("0.4"), groupToC);

        BigDecimal aToB = calculator.calculateIndirectHolding(2L, 3L);
        assertEquals(new BigDecimal("0.5"), aToB);
    }
}